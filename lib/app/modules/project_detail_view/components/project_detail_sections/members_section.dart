import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/components/team_shimmer_effect.dart';
import 'package:incenti_ai/app/modules/user_detail/components/image_picker_bottom_sheet.dart';
import 'package:incenti_ai/app/routes/app_pages.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/models/app_project_team_member_model.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_profile_widget.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/custom_sliver_list_view.dart';
import 'package:incenti_ai/utillites/delete_confirmation_dialog.dart';
import 'package:incenti_ai/utillites/empty.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../../main.dart';
import '../../../../../models/section_base.dart';
import '../../controllers/project_detail_view_controller.dart';

class MembersSection extends SectionBase<ProjectDetailViewController> {
  MembersSection({required super.controller});

  @override
  String get title => 'Members';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();
        Get.toNamed(Routes.project_member,
            arguments: {"projectId": controller.projectId.value})?.then(
          (value) {
            controller.teamMemberDataList.clear();
            controller.callApiForGetProjectTeamMember(context: context);
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(child: Space.height(15)),
      Obx(() {
        return controller.isLoading.value &&
                controller.teamMemberDataList.isEmpty
            ? SliverToBoxAdapter(
                child: ListView.builder(
                itemCount: 5,
                shrinkWrap: true,
                padding: EdgeInsets.only(top: MySize.size10 ?? 10),
                controller: ScrollController(),
                itemBuilder: (context, index) {
                  return TeamMemberTileShimmer();
                },
              ))
            : CustomSliverListView(
                emptyWidget: Padding(
                  padding: EdgeInsets.only(
                    top: MySize.size10 ?? 100,
                    left: paddingHoriZontal,
                    right: paddingHoriZontal,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(top: MySize.size50 ?? 50),
                    child: const Empty(
                      title: "No Member available!",
                    ),
                  ),
                ),
                maximumReachedWidget: const SizedBox(),
                itemBuilder:
                    (context, ProjectTeamMemberData projectTeamMember, index) {
                  return ListTile(
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(30)),
                      leading: profileImage(
                        url: controller.teamMemberDataList[index].user?.image ??
                            "",
                        userName: controller
                                .teamMemberDataList[index].user?.firstName ??
                            "",
                        iconHeight: MySize.getScaledSizeHeight(60),
                        iconWidth: MySize.getScaledSizeWidth(60),
                        height: MySize.getScaledSizeHeight(60),
                        width: MySize.getScaledSizeWidth(60),
                      ),
                      title: TypoGraphy(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        text:
                            "${controller.teamMemberDataList[index].user?.firstName} ${controller.teamMemberDataList[index].user?.lastName}",
                        level: 12,
                        // color: AppTheme.baseBlack,
                      ),
                      subtitle: Row(
                        children: [
                          TypoGraphy(
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            text: controller
                                        .teamMemberDataList[index].user?.id ==
                                    CurrentUser.user.id
                                ? "You"
                                : controller.teamMemberDataList[index].access ==
                                        "read"
                                    ? "View only"
                                    : "Can edit",
                            level: 2,
                            fontWeight: FontWeight.w400,
                            color: AppTheme.grey,
                          ),
                          TypoGraphy(
                            text: controller.teamMemberDataList[index].status ==
                                    null
                                ? " ( Pending... )"
                                : controller.teamMemberDataList[index].status ==
                                        false
                                    ? " ( Reject )"
                                    : "",
                            level: 3,
                            fontWeight: FontWeight.w400,
                            color: controller
                                        .teamMemberDataList[index].status ==
                                    false
                                ? AppTheme.red
                                : controller.teamMemberDataList[index].status ==
                                        null
                                    ? AppTheme.yellow
                                    : AppTheme.grey,
                          ),
                        ],
                      ),
                      trailing: (controller.teamMemberDataList.value[index]
                                      .addedByUser?.id ==
                                  CurrentUser.user.id ||
                              CurrentUser.user.id ==
                                  controller.getProjectDetailData.value.userId)
                          ? InkWell(
                              onTap: () {
                                ImagePickerBottomSheet.show(
                                  context: context,
                                  child: Column(
                                    children: [
                                      Space.height(39),
                                      if (controller.teamMemberDataList[index]
                                              .status !=
                                          false) ...[
                                        InkWell(
                                          onTap: () {
                                            HapticFeedback.lightImpact();
                                            controller.selectedIndex.value = 0;
                                            controller
                                                .callApiForProjectMemberRole(
                                              context: context,
                                              projectId: controller
                                                  .teamMemberDataList[index].id
                                                  .toString(),
                                              access: "read",
                                            )
                                                .then(
                                              (value) {
                                                controller.teamMemberDataList
                                                    .clear();
                                                controller
                                                    .callApiForGetProjectTeamMember(
                                                        context: context);
                                              },
                                            );
                                          },
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                              horizontal:
                                                  MySize.getScaledSizeWidth(30),
                                            ),
                                            child: Row(
                                              children: [
                                                Image.asset(
                                                  AppImage.viewOnly,
                                                  height: MySize
                                                      .getScaledSizeHeight(24),
                                                  width: MySize
                                                      .getScaledSizeHeight(24),
                                                  color: controller
                                                              .teamMemberDataList[
                                                                  index]
                                                              .access ==
                                                          "read"
                                                      ? AppTheme.primary1
                                                      : AppTheme.whiteWithBase,
                                                ),
                                                Space.width(20),
                                                TypoGraphy(
                                                  text: "View only",
                                                  level: 5,
                                                  color: controller
                                                              .teamMemberDataList[
                                                                  index]
                                                              .access ==
                                                          "read"
                                                      ? AppTheme.primary1
                                                      : AppTheme.whiteWithBase,
                                                ),
                                                Spacer(),
                                                if (controller
                                                        .teamMemberDataList[
                                                            index]
                                                        .access ==
                                                    "read")
                                                  SvgPicture.asset(
                                                      AppImage.checkIcon,
                                                      height: MySize
                                                          .getScaledSizeHeight(
                                                              24),
                                                      width: MySize
                                                          .getScaledSizeHeight(
                                                              24),
                                                      color: AppTheme.primary1),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Space.height(40),
                                        InkWell(
                                          onTap: () {
                                            HapticFeedback.lightImpact();
                                            controller.selectedIndex.value = 1;
                                            controller
                                                .callApiForProjectMemberRole(
                                              context: context,
                                              projectId: controller
                                                  .teamMemberDataList[index].id
                                                  .toString(),
                                              access: "write",
                                            )
                                                .then(
                                              (value) {
                                                controller.teamMemberDataList
                                                    .clear();
                                                controller
                                                    .callApiForGetProjectTeamMember(
                                                        context: context);
                                              },
                                            );
                                          },
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal:
                                                    MySize.getScaledSizeWidth(
                                                        30)),
                                            child: Row(
                                              children: [
                                                Image.asset(
                                                  AppImage.editMember,
                                                  height: MySize
                                                      .getScaledSizeHeight(24),
                                                  width: MySize
                                                      .getScaledSizeHeight(24),
                                                  color: controller
                                                              .teamMemberDataList[
                                                                  index]
                                                              .access ==
                                                          "write"
                                                      ? AppTheme.primary1
                                                      : AppTheme.whiteWithBase,
                                                ),
                                                Space.width(20),
                                                TypoGraphy(
                                                  text: "Can edit",
                                                  level: 5,
                                                  color: controller
                                                              .teamMemberDataList[
                                                                  index]
                                                              .access ==
                                                          "write"
                                                      ? AppTheme.primary1
                                                      : AppTheme.whiteWithBase,
                                                ),
                                                Spacer(),
                                                if (controller
                                                        .teamMemberDataList[
                                                            index]
                                                        .access ==
                                                    "write")
                                                  SvgPicture.asset(
                                                      AppImage.checkIcon,
                                                      height: MySize
                                                          .getScaledSizeHeight(
                                                              24),
                                                      width: MySize
                                                          .getScaledSizeHeight(
                                                              24),
                                                      color: AppTheme.primary1),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Space.height(40),
                                      ],
                                      InkWell(
                                        onTap: () {
                                          Navigator.pop(Get.context!);
                                          HapticFeedback.heavyImpact();
                                          showDeleteConfirmationDialog(
                                            context: Get.context!,
                                            description:
                                                "Are you sure you want to remove Team Member permanently.",
                                            onConfirm: () async {
                                              Navigator.pop(Get.context!);
                                              controller
                                                  .callApiForDeleteTeamMember(
                                                      context: context,
                                                      teamId: controller
                                                          .teamMemberDataList[
                                                              index]
                                                          .id,
                                                      userId: controller
                                                              .teamMemberDataList[
                                                                  index]
                                                              .userId ??
                                                          0)
                                                  .then(
                                                (value) {
                                                  controller.teamMemberDataList
                                                      .clear();
                                                  controller
                                                      .callApiForGetProjectTeamMember(
                                                          context: context);
                                                },
                                              );
                                            },
                                            title: "Remove Team Member",
                                            onCancel: () {
                                              Get.back();
                                            }, isLoading: controller.isLoading,
                                          );
                                        },
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal:
                                                  MySize.getScaledSizeWidth(
                                                      30)),
                                          child: Row(
                                            children: [
                                              SvgPicture.asset(
                                                AppImage.deleteTodo,
                                                height:
                                                    MySize.getScaledSizeHeight(
                                                        24),
                                                width:
                                                    MySize.getScaledSizeHeight(
                                                        24),
                                                color: AppTheme.red,
                                              ),
                                              Space.width(20),
                                              TypoGraphy(
                                                text: "Remove",
                                                level: 5,
                                                color: AppTheme.red,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Space.height(60),
                                    ],
                                  ),
                                );
                              },
                              child: SvgPicture.asset(
                                AppImage.moreVertIcon,
                                height: MySize.getScaledSizeWidth(24),
                                width: MySize.getScaledSizeHeight(24),
                                color: box.read('isDarkMode')
                                    ? AppTheme.grey
                                    : null,
                              ),
                            )
                          : CurrentUser.user.id ==
                                  controller.teamMemberDataList[index].userId
                              ? InkWell(
                                  onTap: () {
                                    HapticFeedback.heavyImpact();
                                    showDeleteConfirmationDialog(
                                      context: Get.context!,
                                      description:
                                          "Are you sure you want to Leave The Team permanently?",
                                      onConfirm: () async {
                                        Navigator.pop(Get.context!);
                                        controller
                                            .callApiForDeleteTeamMember(
                                                context: context,
                                                teamId: controller
                                                    .teamMemberDataList[index]
                                                    .id,
                                                isLeaveTeam: true,
                                                userId: controller
                                                        .teamMemberDataList[
                                                            index]
                                                        .userId ??
                                                    0)
                                            .then(
                                          (value) {
                                            controller.teamMemberDataList
                                                .clear();
                                            controller
                                                .callApiForGetProjectTeamMember(
                                                    context: context);
                                          },
                                        );
                                      },
                                      title: "Leave Team",
                                      onCancel: () {
                                        Get.back();
                                      }, isLoading: controller.isLoading,
                                    );
                                  },
                                  child: Icon(
                                    Icons.logout,
                                    color: AppTheme.red,
                                  ),
                                )
                              : SizedBox());
                },
                isLoading: controller.isLoading.value,
                items: controller.teamMemberDataList,
                hasMoreData: controller.hasMoreData.value,
                onLoadMore: () {
                  return controller.callApiForGetProjectTeamMember(
                    context: context,
                  );
                },
              );
      }),
      SliverToBoxAdapter(child: Space.height(Platform.isAndroid ? 25 : 80)),
    ];
  }

  @override
  void onCategorySelected() {
    controller.teamMemberDataList.clear();
    controller.callApiForGetProjectTeamMember(context: Get.context!);
  }
}
