import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/bottom_bar/controllers/bottom_bar_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/models/story_model.dart';
import 'package:incenti_ai/services/app_link_service.dart';
import 'package:incenti_ai/utillites/common_post_like_bottomsheet.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_post_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/comment_bottomsheet_view.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/common_shimmer_effect.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../routes/app_pages.dart';
import '../components/common_post_widget.dart';
import '../controllers/explore_controller.dart';

class ExploreView extends GetWidget<ExploreController> {
  const ExploreView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      // backgroundColor: AppTheme.white,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            controller.pullRefresh();
          },
          child: CustomScrollView(
            physics: ClampingScrollPhysics(),
            controller: Get.find<BottomBarController>().scrollController,
            slivers: [
              SliverAppBar(
                pinned: false,
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.darkBackground
                    : AppTheme.white,
                automaticallyImplyLeading: false,
                floating: true,
                elevation: 0,
                surfaceTintColor: Colors.transparent,
                toolbarHeight: MySize.size62 ?? 60,
                flexibleSpace: Padding(
                  padding: EdgeInsets.only(top: MySize.size5 ?? 60),
                  child: Center(
                    child: Row(
                      // crossAxisAlignment: CrossAxisAlignment.center,
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Space.width(30),
                        Image.asset(
                          "assets/images/new_logo.png",
                          color: AppTheme.whiteWithBase,
                          width: MySize.getScaledSizeWidth(100),
                          // height: MySize.size60,
                        ),
                        Spacer(),
                        InkWell(
                          onTap: () {
                            Get.toNamed(Routes.global_search);
                          },
                          child: SvgPicture.asset(
                            AppImage.searchIcon,
                            width: MySize.size24,
                            height: MySize.size24,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppTheme.grey
                                    : null,
                          ),
                        ),
                        Space.width(25),
                        InkWell(
                          onTap: () {
                            Get.toNamed(Routes.notifications)?.then(
                              (value) {
                                CurrentUser.user.newNotifications?.value =
                                    false;
                              },
                            );
                          },
                          child: Obx(
                            () => Badge(
                              isLabelVisible:
                                  CurrentUser.user.newNotifications?.value ??
                                      false,
                              smallSize: 8,
                              backgroundColor: AppTheme.primary1,
                              child: SvgPicture.asset(
                                AppImage.notificationIcon,
                                width: MySize.size24,
                                height: MySize.size24,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? AppTheme.grey
                                    : null,
                              ),
                            ),
                          ),
                        ),
                        Space.width(25),
                        InkWell(
                          onTap: () {
                            Get.toNamed(Routes.profile,
                                arguments: {"isNeedBottom": true});
                            CurrentUser.getMe(callback: () async {});
                          },
                          child: Obx(
                            () => profileImage(
                                url: CurrentUser.user.image ?? "",
                                width: MySize.size34 ?? 25,
                                height: MySize.size34 ?? 25,
                                iconHeight: MySize.size34 ?? 25,
                                iconWidth: MySize.size34 ?? 25,
                                borderColor: Colors.transparent,
                                color: AppTheme.darkGrey[100]),
                          ),
                        ),
                        Space.width(30)
                      ],
                    ),
                  ),
                ),
              ),
              Obx(
                () => (controller.isExploreLoading.value)
                    ? SliverToBoxAdapter(
                        child: Container(
                          height: MySize.getScaledSizeHeight(102),
                          margin: EdgeInsets.only(
                            top: MySize.getScaledSizeHeight(8),
                          ),
                          alignment: Alignment.centerLeft,
                          child: ListView.builder(
                            padding: EdgeInsets.only(
                              left: MySize.getScaledSizeWidth(20),
                            ),
                            shrinkWrap: true,
                            itemCount: 5,
                            scrollDirection: Axis.horizontal,
                            physics: const BouncingScrollPhysics(),
                            itemBuilder: (context, index) {
                              return Column(
                                children: [
                                  Shimmer.fromColors(
                                    baseColor: AppTheme.baseShimmer,
                                    highlightColor: AppTheme.highlightShimmer,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        right: MySize.getScaledSizeWidth(12),
                                      ),
                                      height: MySize.getScaledSizeHeight(70),
                                      width: MySize.getScaledSizeHeight(70),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.grey[400],
                                      ),
                                    ),
                                  ),
                                  Space.height(10),
                                  Shimmer.fromColors(
                                    baseColor: AppTheme.baseShimmer,
                                    highlightColor: AppTheme.highlightShimmer,
                                    // baseColor: Colors.grey[300]!,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        right: MySize.getScaledSizeWidth(12),
                                      ),
                                      height: MySize.getScaledSizeHeight(15),
                                      width: MySize.getScaledSizeHeight(60),
                                      decoration: BoxDecoration(
                                          color: Colors.grey[400],
                                          borderRadius:
                                              BorderRadius.circular(5)),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      )
                    : SliverToBoxAdapter(
                        child: Column(
                          children: [
                            Container(
                              height: MySize.getScaledSizeHeight(120),
                              alignment: Alignment.centerLeft,
                              child: Obx(() {
                                final currentUserId = CurrentUser.user.id;

                                final hasCurrentUserStory =
                                    controller.storyDataList.any((storyUser) =>
                                        storyUser.id == currentUserId &&
                                        (storyUser.stories?.isNotEmpty ??
                                            false));

                                final List<Story> visibleStories = [];

                                if (!hasCurrentUserStory) {
                                  visibleStories.add(
                                      Story(id: currentUserId, stories: []));
                                }

                                visibleStories.addAll(controller.storyDataList);
                                return ListView.builder(
                                  padding: EdgeInsets.only(
                                      left: MySize.getScaledSizeWidth(15)),
                                  shrinkWrap: true,
                                  itemCount: visibleStories.length,
                                  scrollDirection: Axis.horizontal,
                                  physics: const BouncingScrollPhysics(),
                                  itemBuilder: (context, index) {
                                    final storyUser = visibleStories[index];
                                    final stories = storyUser.stories ?? [];

                                    final isCurrentUser =
                                        storyUser.id == currentUserId;

                                    if (stories.isNotEmpty) {
                                      return userStoryView(
                                        mentioned:
                                            stories.last.mentionedPath ?? '',
                                        isUploading: isCurrentUser
                                            ? controller.isUploading
                                            : RxBool(false),
                                        overlayImage:
                                            stories.last.overlayImage ?? '',
                                        profileImageUrl: storyUser.image ?? '',
                                        thumbnail: stories.last.mediaType ==
                                                StoryType.video.name
                                            ? stories.last.thumbnailPath ?? ''
                                            : stories.last.mediaLink ?? '',
                                        userName: storyUser.firstName ?? '',
                                        onTap: () {
                                          Get.toNamed(Routes.all_stories_view,
                                              arguments: {
                                                'storiesList':
                                                    controller.storyDataList,
                                                'index': hasCurrentUserStory
                                                    ? index
                                                    : index - 1,
                                                'isHighlight': false,
                                              });
                                        },
                                      );
                                    } else {
                                      return isCurrentUser
                                          ? Obx(() {
                                              return storyNotAddedView(
                                                  isUploading: controller
                                                      .isUploading.value,
                                                  context: context);
                                            })
                                          : const SizedBox.shrink();
                                    }
                                  },
                                );
                              }),
                            ),
                            // Feed View
                          ],
                        ),
                      ),
              ),
              Obx(() {
                return controller.isExploreLoading.value &&
                        controller.postDataList.isEmpty
                    ? SliverToBoxAdapter(
                        child: ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        // Disable scrolling inside CustomScrollView
                        shrinkWrap: true,
                        itemCount: 5,
                        itemBuilder: (context, index) => ShimmerPostCard(),
                      ))
                    : CustomSliverListView(
                        emptyWidget: Padding(
                          padding: EdgeInsets.only(
                            top: MySize.getScaledSizeHeight(250),
                          ),
                          child: const Empty(
                            title: "Follow People or Project show Feed!",
                          ),
                        ),
                        maximumReachedWidget: const SizedBox(),
                        itemBuilder: (context, Post post, index) {
                          Post res = controller.postDataList[index];
                          return res.isPrivate == true
                              ? const SizedBox()
                              : Obx(
                                  () => commonFeedView(
                                    res,
                                    index,
                                    context,
                                    isExplore: true,
                                    blockCallBack: () {
                                      HapticFeedback.lightImpact();
                                      controller.callApiForBlockUser(
                                          context: context,
                                          userId: res.user?.id ?? 0);
                                    },
                                    isUser: res.user?.id == CurrentUser.user.id,
                                    onRepostTap: () async {
                                      Navigator.pop(context);
                                      showCommonReportBottomSheet(
                                        context: context,
                                        title: "Report",
                                        subTitle:
                                            "Why are you reporting this post?",
                                        description:
                                            "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                        options: controller.repostData,
                                        onOptionTap: (selectedOption) async {
                                          controller.selectedReason.value =
                                              selectedOption;
                                          await controller.callApiForReportPost(
                                            context: context,
                                            postId: res.id.toString(),
                                          );
                                        },
                                      );
                                    },
                                    onLikeLongPress: () {
                                      controller.postLikeList.clear();
                                      controller.callApiForGetPostLike(
                                          context: context, postId: res.id);
                                      showModalBottomSheet(
                                        context: context,
                                        isScrollControlled: true,
                                        backgroundColor: Colors.transparent,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                            top: Radius.circular(16),
                                          ),
                                        ),
                                        builder: (context) =>
                                            CommonPostLikeBottomSheet(
                                          index: index,
                                          isLoading: controller.isLoading,
                                          postLikeList: controller.postLikeList,
                                        ),
                                      );
                                    },
                                    onLike: () =>
                                        controller.callApiForLikeProject(
                                      context: context,
                                      postId: res.id.toString(),
                                      index: index,
                                    ),
                                    onComment: () {
                                      controller.commentController.clear();
                                      controller.callApiForGetCommentProject(
                                          context: context,
                                          postId: res.id.toString(),
                                          index: index);
                                      showModalBottomSheet(
                                        context: context,
                                        isScrollControlled: true,
                                        backgroundColor: Colors.transparent,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                            top: Radius.circular(16),
                                          ),
                                        ),
                                        builder: (context) => CommonBottomSheet(
                                          index: index,
                                          isLoading: controller.isLoading,
                                          commentController:
                                              controller.commentController,
                                          commentDataList:
                                              controller.commentDataList,
                                          commentFocusNode:
                                              controller.commentFocusNode,
                                          userId: res.user?.id ?? 0,
                                          onCommentSend: () {
                                            controller.callApiForCommentProject(
                                                context: context,
                                                postId:
                                                    "${controller.postDataList[index].id}",
                                                index: index);
                                            controller.commentController
                                                .clear();
                                          },
                                        ),
                                      ).then((value) {
                                        controller.callApiForGetCommentProject(
                                            context: context,
                                            postId: res.id.toString(),
                                            index: index);
                                      },);
                                    },
                                    onBookmark: () =>
                                        controller.callApiForBookMarkProject(
                                      context: context,
                                      postId: res.id.toString(),
                                      index: index,
                                    ),
                                    isLiked: res.isLiked?.value ?? false,
                                    likesCount: res.likesCount?.value ?? 0,
                                    isBookmarked:
                                        res.isBookMarked?.value ?? false,
                                    commentsCount:
                                    res.commentsCount?.value ?? 0,
                                    onShare: () {
                                      HapticFeedback.lightImpact();
                                      AppLinkService().shareMedia(
                                          slug: res.slug ?? '',
                                          mediaType: ShareMediaType.posts,
                                          title: res.title);
                                    },
                                  ),
                                );
                        },
                        isLoading: controller.isExploreLoading.value,
                        items: controller.postDataList,
                        hasMoreData: controller.hasMoreData.value,
                        onLoadMore: () {
                          return controller.callApiForExplorePost(
                            context: context,
                          );
                        },
                      );
              })
            ],
          ),
        ),
      ),
    );
  }
}
